import { invoke } from '@tauri-apps/api/core';
import type { FilterConfig, FilterCondition } from '$lib/types/filterConfig';

export class FilterConfigService {
  // 获取所有筛选配置
  async getFilterConfigs(): Promise<FilterConfig[]> {
    try {
      return await invoke('get_filter_configs');
    } catch (error) {
      console.error('获取筛选配置失败:', error);
      return [];
    }
  }

  // 获取单个筛选配置
  async getFilterConfig(id: string): Promise<FilterConfig | null> {
    try {
      return await invoke('get_filter_config', { id });
    } catch (error) {
      console.error('获取筛选配置失败:', error);
      return null;
    }
  }

  // 保存筛选配置
  async saveFilterConfig(config: Omit<FilterConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<FilterConfig> {
    try {
      return await invoke('save_filter_config', { config });
    } catch (error) {
      console.error('保存筛选配置失败:', error);
      throw error;
    }
  }

  // 更新筛选配置
  async updateFilterConfig(id: string, config: Partial<FilterConfig>): Promise<FilterConfig> {
    try {
      return await invoke('update_filter_config', { id, config });
    } catch (error) {
      console.error('更新筛选配置失败:', error);
      throw error;
    }
  }

  // 删除筛选配置
  async deleteFilterConfig(id: string): Promise<void> {
    try {
      await invoke('delete_filter_config', { id });
    } catch (error) {
      console.error('删除筛选配置失败:', error);
      throw error;
    }
  }

  // 应用筛选配置到项目查询
  applyFilterConfigToQuery(config: FilterConfig, baseQuery: any = {}): any {
    const query = { ...baseQuery };

    config.conditions.forEach(condition => {
      switch (condition.field) {
        case 'project_status_item_id':
        case 'recruitment_status_item_id':
        case 'disease_item_id':
        case 'project_stage_item_id':
          if (condition.operator === 'equals') {
            query[condition.field] = condition.value;
          }
          break;
        
        case 'sponsor_item_ids':
        case 'pi_personnel_ids':
        case 'crc_personnel_ids':
          if (condition.operator === 'in' && Array.isArray(condition.value)) {
            query[condition.field] = condition.value;
          }
          break;
        
        case 'project_name':
          if (condition.operator === 'contains') {
            query.name = condition.value;
          }
          break;
        
        case 'project_start_date':
          if (condition.operator === 'between' && Array.isArray(condition.value)) {
            query.start_date_from = condition.value[0];
            query.start_date_to = condition.value[1];
          }
          break;
      }
    });

    return query;
  }

  // 从查询参数生成筛选配置
  generateFilterConfigFromQuery(query: any, name: string): FilterConfig {
    const conditions: FilterCondition[] = [];

    // 项目状态
    if (query.project_status_item_id) {
      conditions.push({
        field: 'project_status_item_id',
        operator: 'equals',
        value: query.project_status_item_id
      });
    }

    // 招募状态
    if (query.recruitment_status_item_id) {
      conditions.push({
        field: 'recruitment_status_item_id',
        operator: 'equals',
        value: query.recruitment_status_item_id
      });
    }

    // 疾病类型
    if (query.disease_item_id) {
      conditions.push({
        field: 'disease_item_id',
        operator: 'equals',
        value: query.disease_item_id
      });
    }

    // 研究分期
    if (query.project_stage_item_id) {
      conditions.push({
        field: 'project_stage_item_id',
        operator: 'equals',
        value: query.project_stage_item_id
      });
    }

    // 项目名称
    if (query.name) {
      conditions.push({
        field: 'project_name',
        operator: 'contains',
        value: query.name
      });
    }

    // 日期范围
    if (query.start_date_from && query.start_date_to) {
      conditions.push({
        field: 'project_start_date',
        operator: 'between',
        value: [query.start_date_from, query.start_date_to]
      });
    }

    return {
      name,
      conditions,
      createdAt: new Date().toISOString()
    };
  }
}

export const filterConfigService = new FilterConfigService();
