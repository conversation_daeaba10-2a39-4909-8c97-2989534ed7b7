<script lang="ts">
  import { cn } from "$lib/utils";
  import { X, Edit3 } from 'lucide-svelte';
  import type { QuickFilterTag } from '$lib/types/filterConfig';

  // Props
  export let tag: QuickFilterTag;
  export let showActions = false;
  export let onEdit: ((tag: QuickFilterTag) => void) | null = null;
  export let onDelete: ((tag: QuickFilterTag) => void) | null = null;

  // 获取颜色样式
  function getColorClasses(color: string = 'blue') {
    const colorMap: Record<string, {
      bg: string,
      activeBg: string,
      text: string,
      activeText: string,
      border: string,
      activeBorder: string
    }> = {
      blue: {
        bg: "bg-blue-50",
        activeBg: "bg-blue-500",
        text: "text-blue-700",
        activeText: "text-white",
        border: "border-blue-200",
        activeBorder: "border-blue-500"
      },
      green: {
        bg: "bg-green-50",
        activeBg: "bg-green-500",
        text: "text-green-700",
        activeText: "text-white",
        border: "border-green-200",
        activeBorder: "border-green-500"
      },
      purple: {
        bg: "bg-purple-50",
        activeBg: "bg-purple-500",
        text: "text-purple-700",
        activeText: "text-white",
        border: "border-purple-200",
        activeBorder: "border-purple-500"
      },
      orange: {
        bg: "bg-orange-50",
        activeBg: "bg-orange-500",
        text: "text-orange-700",
        activeText: "text-white",
        border: "border-orange-200",
        activeBorder: "border-orange-500"
      },
      gray: {
        bg: "bg-gray-50",
        activeBg: "bg-gray-500",
        text: "text-gray-700",
        activeText: "text-white",
        border: "border-gray-200",
        activeBorder: "border-gray-500"
      }
    };

    return colorMap[color] || colorMap.blue;
  }

  const colorClasses = getColorClasses(tag.color);

  // 处理点击
  function handleClick() {
    if (tag.onClick) {
      tag.onClick();
    }
  }

  // 处理编辑
  function handleEdit(event: MouseEvent) {
    event.stopPropagation();
    if (onEdit) {
      onEdit(tag);
    }
  }

  // 处理删除
  function handleDelete(event: MouseEvent) {
    event.stopPropagation();
    if (onDelete) {
      onDelete(tag);
    }
  }
</script>

<div class="relative group">
  <button
    class={cn(
      "inline-flex items-center justify-center px-3 py-2 rounded-full border transition-all duration-200 cursor-pointer",
      "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2",
      tag.isActive 
        ? `${colorClasses.activeBg} ${colorClasses.activeText} ${colorClasses.activeBorder} shadow-md`
        : `${colorClasses.bg} ${colorClasses.text} ${colorClasses.border} hover:${colorClasses.activeBg} hover:${colorClasses.activeText}`,
      "min-h-[48px] text-sm font-medium"
    )}
    onclick={handleClick}
    aria-label={`应用筛选: ${tag.label}`}
  >
    <div class="flex items-center gap-2">
      <span>{tag.label}</span>
      {#if tag.count !== undefined}
        <span class={cn(
          "text-xs px-2 py-0.5 rounded-full",
          tag.isActive 
            ? "bg-white bg-opacity-20" 
            : "bg-gray-100"
        )}>
          {tag.count}
        </span>
      {/if}
    </div>
  </button>

  <!-- 操作按钮 -->
  {#if showActions && (onEdit || onDelete)}
    <div class="absolute -top-2 -right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-1">
      {#if onEdit}
        <button
          onclick={handleEdit}
          class="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-sm"
          aria-label="编辑筛选配置"
          title="编辑"
        >
          <Edit3 class="h-3 w-3" />
        </button>
      {/if}
      
      {#if onDelete}
        <button
          onclick={handleDelete}
          class="w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors shadow-sm"
          aria-label="删除筛选配置"
          title="删除"
        >
          <X class="h-3 w-3" />
        </button>
      {/if}
    </div>
  {/if}
</div>
