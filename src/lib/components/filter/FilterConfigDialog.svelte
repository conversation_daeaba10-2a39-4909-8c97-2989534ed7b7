<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import { X, Plus, Trash2 } from 'lucide-svelte';
  import type { FilterConfig, FilterCondition, FilterField } from '$lib/types/filterConfig';
  import { FILTER_FIELDS } from '$lib/types/filterConfig';

  // Props
  export let isOpen = false;
  export let config: FilterConfig | null = null;
  export let availableOptions: Record<string, Array<{ value: any; label: string }>> = {};

  // Events
  const dispatch = createEventDispatcher<{
    save: FilterConfig;
    cancel: void;
  }>();

  // State
  let formData: Omit<FilterConfig, 'id' | 'createdAt' | 'updatedAt'> = {
    name: '',
    description: '',
    conditions: []
  };

  // 当配置变化时重置表单
  $: if (config) {
    formData = {
      name: config.name || '',
      description: config.description || '',
      conditions: [...(config.conditions || [])]
    };
  } else {
    formData = {
      name: '',
      description: '',
      conditions: []
    };
  }

  // 添加新条件
  function addCondition() {
    formData.conditions = [...formData.conditions, {
      field: '',
      operator: 'equals',
      value: null
    }];
  }

  // 删除条件
  function removeCondition(index: number) {
    formData.conditions = formData.conditions.filter((_, i) => i !== index);
  }

  // 更新条件
  function updateCondition(index: number, updates: Partial<FilterCondition>) {
    formData.conditions = formData.conditions.map((condition, i) => 
      i === index ? { ...condition, ...updates } : condition
    );
  }

  // 获取字段配置
  function getFieldConfig(fieldKey: string): FilterField | undefined {
    return FILTER_FIELDS.find(f => f.key === fieldKey);
  }

  // 获取字段选项
  function getFieldOptions(fieldKey: string): Array<{ value: any; label: string }> {
    return availableOptions[fieldKey] || [];
  }

  // 保存配置
  function handleSave() {
    if (!formData.name.trim()) {
      alert('请输入配置名称');
      return;
    }

    if (formData.conditions.length === 0) {
      alert('请至少添加一个筛选条件');
      return;
    }

    // 验证条件
    for (const condition of formData.conditions) {
      if (!condition.field) {
        alert('请选择筛选字段');
        return;
      }
      if (condition.value === null || condition.value === undefined || condition.value === '') {
        alert('请设置筛选值');
        return;
      }
    }

    dispatch('save', formData);
  }

  // 取消
  function handleCancel() {
    dispatch('cancel');
  }
</script>

{#if isOpen}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200">
        <h2 class="text-lg font-semibold text-gray-900">
          {config ? '编辑筛选配置' : '新建筛选配置'}
        </h2>
        <button
          onclick={handleCancel}
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <X class="h-5 w-5" />
        </button>
      </div>

      <!-- 内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
        <!-- 基本信息 -->
        <div class="space-y-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              配置名称 <span class="text-red-500">*</span>
            </label>
            <Input
              bind:value={formData.name}
              placeholder="输入配置名称"
              class="w-full"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              描述
            </label>
            <Input
              bind:value={formData.description}
              placeholder="输入配置描述（可选）"
              class="w-full"
            />
          </div>
        </div>

        <!-- 筛选条件 -->
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <h3 class="text-sm font-medium text-gray-700">筛选条件</h3>
            <Button
              variant="outline"
              size="sm"
              onclick={addCondition}
              class="flex items-center gap-2"
            >
              <Plus class="h-4 w-4" />
              添加条件
            </Button>
          </div>

          {#if formData.conditions.length === 0}
            <div class="text-center py-8 text-gray-500">
              <p>暂无筛选条件，点击"添加条件"开始配置</p>
            </div>
          {:else}
            <div class="space-y-3">
              {#each formData.conditions as condition, index}
                <div class="flex items-center gap-3 p-3 border border-gray-200 rounded-lg">
                  <!-- 字段选择 -->
                  <div class="flex-1">
                    <select
                      bind:value={condition.field}
                      onchange={() => updateCondition(index, { field: condition.field, value: null })}
                      class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    >
                      <option value="">选择字段</option>
                      {#each FILTER_FIELDS as field}
                        <option value={field.key}>{field.label}</option>
                      {/each}
                    </select>
                  </div>

                  <!-- 值输入 -->
                  <div class="flex-1">
                    {#if condition.field}
                      {@const fieldConfig = getFieldConfig(condition.field)}
                      {#if fieldConfig?.type === 'select'}
                        <select
                          bind:value={condition.value}
                          class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                        >
                          <option value="">选择值</option>
                          {#each getFieldOptions(condition.field) as option}
                            <option value={option.value}>{option.label}</option>
                          {/each}
                        </select>
                      {:else if fieldConfig?.type === 'text'}
                        <Input
                          bind:value={condition.value}
                          placeholder={fieldConfig.placeholder}
                          class="w-full"
                        />
                      {:else}
                        <Input
                          bind:value={condition.value}
                          placeholder="输入值"
                          class="w-full"
                        />
                      {/if}
                    {:else}
                      <Input
                        disabled
                        placeholder="请先选择字段"
                        class="w-full"
                      />
                    {/if}
                  </div>

                  <!-- 删除按钮 -->
                  <Button
                    variant="outline"
                    size="sm"
                    onclick={() => removeCondition(index)}
                    class="flex-shrink-0 text-red-600 hover:text-red-800"
                  >
                    <Trash2 class="h-4 w-4" />
                  </Button>
                </div>
              {/each}
            </div>
          {/if}
        </div>
      </div>

      <!-- 底部按钮 -->
      <div class="flex items-center justify-end gap-3 p-6 border-t border-gray-200">
        <Button variant="outline" onclick={handleCancel}>
          取消
        </Button>
        <Button onclick={handleSave}>
          保存配置
        </Button>
      </div>
    </div>
  </div>
{/if}
