use tauri::State;
use std::sync::Mutex;
use rusqlite::Connection;
use crate::models::filter_config::{FilterConfig, CreateFilterConfigRequest, UpdateFilterConfigRequest};
use crate::services::filter_config_service::FilterConfigService;

#[tauri::command]
pub async fn init_filter_config_tables(
    db: State<'_, Mutex<Connection>>,
) -> Result<(), String> {
    let conn = db.lock().map_err(|e| format!("数据库锁定失败: {}", e))?;
    FilterConfigService::init_tables(&conn)
}

#[tauri::command]
pub async fn get_filter_configs(
    db: State<'_, Mutex<Connection>>,
) -> Result<Vec<FilterConfig>, String> {
    let conn = db.lock().map_err(|e| format!("数据库锁定失败: {}", e))?;
    FilterConfigService::get_all_configs(&conn)
}

#[tauri::command]
pub async fn get_filter_config(
    id: String,
    db: State<'_, Mutex<Connection>>,
) -> Result<Option<FilterConfig>, String> {
    let conn = db.lock().map_err(|e| format!("数据库锁定失败: {}", e))?;
    FilterConfigService::get_config_by_id(&conn, &id)
}

#[tauri::command]
pub async fn save_filter_config(
    config: CreateFilterConfigRequest,
    db: State<'_, Mutex<Connection>>,
) -> Result<FilterConfig, String> {
    let conn = db.lock().map_err(|e| format!("数据库锁定失败: {}", e))?;
    FilterConfigService::create_config(&conn, config)
}

#[tauri::command]
pub async fn update_filter_config(
    id: String,
    config: UpdateFilterConfigRequest,
    db: State<'_, Mutex<Connection>>,
) -> Result<FilterConfig, String> {
    let conn = db.lock().map_err(|e| format!("数据库锁定失败: {}", e))?;
    FilterConfigService::update_config(&conn, &id, config)
}

#[tauri::command]
pub async fn delete_filter_config(
    id: String,
    db: State<'_, Mutex<Connection>>,
) -> Result<(), String> {
    let conn = db.lock().map_err(|e| format!("数据库锁定失败: {}", e))?;
    FilterConfigService::delete_config(&conn, &id)
}
